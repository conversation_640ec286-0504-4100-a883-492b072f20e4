package com.chinastock;

import cn.com.chinastock.cnf.webclient.EnableWebClients;
import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cache.annotation.EnableCaching;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2020/9/9 13:38
 */
@EnableApolloConfig
@EnableCaching
@EnableWebClients
@SpringBootApplication
public class Application {

    public static void main(String[] args) {
        configureClient();
        SpringApplication.run(Application.class, args);
    }

    private static void configureClient() {
        String osName = System.getProperty("os.name").toLowerCase();
        if (osName.toLowerCase().contains("windows") || osName.toLowerCase().contains("mac")) {
            System.setProperty("kafka.topic.consumer.group.id.edw.msg.tmp", String.valueOf(System.currentTimeMillis()));
            System.setProperty("kafka.topic.consumer.group.id.otc.edw.msg.resp", String.valueOf(System.currentTimeMillis()));
        }
    }

}
