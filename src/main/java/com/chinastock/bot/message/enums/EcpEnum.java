package com.chinastock.bot.message.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * @Description 异常枚举
 * <AUTHOR>
 * @Date 2024-08-07 16:21
 **/
@Getter
@RequiredArgsConstructor
public enum EcpEnum {
    SUCCESS("0", "成功"),

    //失败5开头,5XX区分业务模块,500系统级错误
    FAILED("500000", "系统未知异常"),
    MSG_REPEATED("500001","消息重复"),
    REQUEST_ID_NULL("500002","ExternalRequestId不可为空"),
    RESPONSE_ERROR("500003","接口应答失败"),

    ;

    private final String code;
    private final String name;
}
