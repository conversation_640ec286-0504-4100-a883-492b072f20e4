package com.chinastock.bot.message.enums;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/4/25 18:18
 **/
public enum CacheExpireTimeEnum {

    SEC("SEC", 3),
    MIN("MIN", 60),
    MIN_5("MIN_5", 60 * 5),
    MIN_15("MIN_15", 60 * 15),
    HOUR("HOUR", 60 * 60),
    HALF_DAY("HALF_DAY", 60 * 60 * 12),
    DAY("DAY", 60 * 60 * 24),
    WEEK("WEEK", 60 * 60 * 24 * 7),
    NEVER("NEVER", 0);

    private final String code;
    private final Integer value;

    CacheExpireTimeEnum(String code, Integer value) {
        this.code = code;
        this.value = value;
    }

    public static CacheExpireTimeEnum getEnum(String code) {
        CacheExpireTimeEnum[] enums = values();
        for (CacheExpireTimeEnum e : enums) {
            if (e.getCode().equals(code)) {
                return e;
            }
        }
        return null;
    }

    public static CacheExpireTimeEnum getEnumByName(Integer value) {
        CacheExpireTimeEnum[] enums = values();
        for (CacheExpireTimeEnum e : enums) {
            if (e.getValue().equals(value)) {
                return e;
            }
        }
        return null;
    }

    public String getCode() {
        return code;
    }

    public Integer getValue() {
        return value;
    }
}
