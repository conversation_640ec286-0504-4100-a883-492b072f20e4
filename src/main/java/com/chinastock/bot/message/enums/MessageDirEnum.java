package com.chinastock.bot.message.enums;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/4/25 18:18
 **/
public enum MessageDirEnum {

    SEND("我司发送", "send"),
    RECEIVE("我司接收", "receive");

    private final String code;
    private final String value;

    MessageDirEnum(String code, String value) {
        this.code = code;
        this.value = value;
    }

    public static MessageDirEnum getEnum(String code) {
        MessageDirEnum[] enums = values();
        for (MessageDirEnum e : enums) {
            if (e.getCode().equals(code)) {
                return e;
            }
        }
        return null;
    }

    public static MessageDirEnum getEnumByName(String value) {
        MessageDirEnum[] enums = values();
        for (MessageDirEnum e : enums) {
            if (e.getValue().equals(value)) {
                return e;
            }
        }
        return null;
    }

    public String getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }
}
