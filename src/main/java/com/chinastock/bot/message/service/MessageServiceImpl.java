package com.chinastock.bot.message.service;

import cn.com.chinastock.cnf.core.exception.BusinessException;
import com.alibaba.fastjson2.JSON;
import com.chinastock.bot.message.enums.EcpEnum;
import com.chinastock.bot.message.enums.MessageDirEnum;
import com.chinastock.bot.message.manager.MessageManager;
import com.chinastock.bot.message.manager.MqManager;
import com.chinastock.bot.message.manager.remote.MessageSendManager;
import com.chinastock.bot.message.pojo.request.SendMessageRequest;
import com.chinastock.bot.message.pojo.vo.message.ReceiveMessageVo;
import com.chinastock.bot.message.pojo.vo.send.MessageSendResponse;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import reactor.core.publisher.Mono;

import java.util.Map;
import java.util.concurrent.ExecutionException;

/**
 * @Author: David
 * @Date: 2025/8/25 17:15
 * @Description:
 */
@Slf4j
@Service
public class MessageServiceImpl implements MessageService{

    @Value("${kafka.topic.edw.msg.tmp}")
    private String edwMsgTmpTopic;
    @Value("${kafka.topic.edw.msg.req}")
    private String msgReqTopic;
    @Value("${token.dep.map:{\"67e502f67ca1c3f2e00e2941\":\"2714\"}}")
    private String depMapStr;

    @Resource
    private MqManager mqManager;
    @Resource
    private MessageManager messageManager;
    @Resource
    private MessageSendManager messageSendManager;

    @Override
    public void processMessage(ReceiveMessageVo message) throws ExecutionException, InterruptedException {
//        if(messageManager.checkRepeat(MessageDirEnum.RECEIVE.getCode(), message.getReceiveMessageData().getMessageId())) {
//            return;
//        }
//        String depNo = getDepNo(message);
//        if(!StringUtils.hasText(depNo)){
//            log.error("processMessage depNo is null, token:{}", message.getReceiveMessageData().getToken());
//            return;
//        }
//        CustMessage custMessage = messageManager.saveMessage(message, depNo);
        mqManager.sendMessage(msgReqTopic, "20250902-test-msg-005");
    }

    @Override
    public void processMessage(String message) throws ExecutionException, InterruptedException {
        mqManager.sendMessage(msgReqTopic, "20250903-test-bot-req-001");
    }

    @Override
    public Mono<Void> processMessageReactive(ReceiveMessageVo message) {
//        if(messageManager.checkRepeat(MessageDirEnum.RECEIVE.getCode(), message.getReceiveMessageData().getMessageId())) {
//            return Mono.empty();
//        }
//        String depNo = getDepNo(message);
//        if(!StringUtils.hasText(depNo)){
//            log.error("processMessage depNo is null, token:{}", message.getReceiveMessageData().getToken());
//            return Mono.empty();
//        }
//        CustMessage custMessage = messageManager.saveMessage(message, depNo);
        return mqManager.sendMessageReactive(msgReqTopic, "20250902-test-msg-005")
                .then();
    }

    @Override
    public Mono<Void> processMessageReactive(String message) {
        return mqManager.sendMessageReactive(msgReqTopic, "20250903-test-bot-req-001")
                .then();
    }

    private String getDepNo(ReceiveMessageVo message) {
        Map<String, String> depMap = JSON.parseObject(depMapStr, Map.class);
        return depMap.get(message.getReceiveMessageData().getToken());
    }

    @Override
    public boolean testSendMessage(String msg) throws ExecutionException, InterruptedException {
        mqManager.sendMessage(edwMsgTmpTopic, msg);
        return true;
    }

    @Override
    public Mono<MessageSendResponse> sendMessage(SendMessageRequest request) {
        String reqeustStr = JSON.toJSONString(request);
        if(!StringUtils.hasText(request.getExternalRequestId())) {
            log.error("sendMessage error, requestId is null. reqeust:{}", reqeustStr);
            throw new BusinessException(EcpEnum.REQUEST_ID_NULL.getCode(), EcpEnum.REQUEST_ID_NULL.getName());
        }
        if(messageManager.checkRepeat(MessageDirEnum.SEND.getCode(), request.getExternalRequestId())) {
            log.error("sendMessage error, repeat reqeust:{}", reqeustStr);
            throw new BusinessException(EcpEnum.MSG_REPEATED.getCode(), EcpEnum.MSG_REPEATED.getName() + request.getExternalRequestId());
        }
        log.info("sendMessage request: {}", JSON.toJSONString(request));
        Mono<MessageSendResponse> responseMono = messageSendManager.sendMessage(request);
        return responseMono
                .doOnNext(originalResponse -> {
                    // 打印原始响应（未经过任何处理的响应）
                    log.info("sendMessage response: {}", JSON.toJSONString(originalResponse));
                })
                .<MessageSendResponse>handle((response, sink) -> {
                    if ((response.getCode() == null) || response.getCode() != 0) {
                        if(log.isErrorEnabled()) {
                            sink.error(new BusinessException(EcpEnum.RESPONSE_ERROR.getCode()
                                    , EcpEnum.RESPONSE_ERROR.getName() + JSON.toJSONString(response)));
                        }
                        return;
                    }
                    sink.next(response);
                })
                .doOnError(ex -> {
                    if(log.isErrorEnabled()){
                        log.error("sendMessage catch exception, reqeust:{}", reqeustStr, ex);
                    }
                    messageManager.delRepeat(MessageDirEnum.SEND.getCode(), request.getExternalRequestId());
                })
                .doOnSuccess(response -> {
                    log.info("sendMessage success, reqeust:{}, response:{}", reqeustStr, JSON.toJSONString(response));
                });
    }
}
