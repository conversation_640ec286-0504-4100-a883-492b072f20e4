package com.chinastock.bot.message.service;

import com.chinastock.bot.message.pojo.request.SendMessageRequest;
import com.chinastock.bot.message.pojo.vo.message.ReceiveMessageVo;
import com.chinastock.bot.message.pojo.vo.send.MessageSendResponse;
import reactor.core.publisher.Mono;

import java.util.concurrent.ExecutionException;

/**
 * @Author: David
 * @Date: 2025/8/25 17:15
 * @Description:
 */
public interface MessageService {

    /**
     * 处理接收的消息
     * @param message
     */
    void processMessage(ReceiveMessageVo message) throws ExecutionException, InterruptedException;
    void processMessage(String message) throws ExecutionException, InterruptedException;

    /**
     * 处理接收的消息 - Reactive版本
     * @param message
     */
    Mono<Void> processMessageReactive(ReceiveMessageVo message);
    Mono<Void> processMessageReactive(String message);


    /**
     * 测试发送消息
     */
    boolean testSendMessage(String msg) throws ExecutionException, InterruptedException;


    Mono<MessageSendResponse> sendMessage(SendMessageRequest request);
}
