package com.chinastock.bot.message.util;

import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/6/9 10:36
 */
@Service
public class DateUtils {

    public final static String DATE_FORMAT = "yyyyMMdd";
    public final static String DATE_FORMAT_MONTH = "yyyyMM";
    public final static String DATE_FORMAT_LINE = "yyyy-MM-dd";
    public final static String DATE_FORMAT_SLASH = "yyyy/MM/dd";
    public final static String TIME_FORMAT_LINE = "yyyy-MM-dd HH:mm:ss";
    public final static String TIME_FORMAT = "yyyyMMddHHmmss";
    public final static String TIME_FORMAT_FULL_LINE = "yyyy-MM-dd'T'HH:mm:ss.S";
    public final static String TIME_ONLY_FORMAT = "HHmmss";



    public static String dateToString(Date date, String dateformat) {
        SimpleDateFormat fmt = new SimpleDateFormat(dateformat);
        return fmt.format(date);
    }

    /**
     * 获取当天的日期
     */
    public static String getCurrentDateString() {
        return dateToString(new Date(), DATE_FORMAT);
    }

    /**
     * 获取当天的日期
     */
    public static Integer getCurrentDate() {
        return Integer.valueOf(getCurrentDateString());
    }
}
