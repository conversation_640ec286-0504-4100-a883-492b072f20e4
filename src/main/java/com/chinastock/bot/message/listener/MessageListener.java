package com.chinastock.bot.message.listener;

import cn.com.chinastock.cnf.core.log.GalaxyLoggerFactory;
import cn.com.chinastock.cnf.core.log.IGalaxyLogger;
import cn.com.chinastock.cnf.webflux.log.annotation.GalaxySingleTrace;
import com.alibaba.fastjson2.JSON;
import com.chinastock.bot.message.pojo.request.SendMessageRequest;
import com.chinastock.bot.message.service.MessageService;
import jakarta.annotation.Resource;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/6/24 13:18
 */
@Service
public class MessageListener {

    private static final IGalaxyLogger log = GalaxyLoggerFactory.getLogger(MessageListener.class);

    @Resource
    private MessageService messageService;


    @KafkaListener(topics = {"${kafka.topic.edw.msg.tmp}"}
            , groupId = "cnf-test-groupid-1")
    @GalaxySingleTrace(spanName = "messageReceiveListener")
    public Mono<Void> messageReceiveListener(List<ConsumerRecord<String, String>> records) {
        return Flux.fromIterable(records)
                .flatMap(record -> {
                    String message = record.value();
                    log.info("messageReceiveListener receive msg: {},offset:{}", message, record.offset());

                    return messageService.processMessageReactive(message)
                            .doOnError(ex -> {
                                log.error("MessageListener process error. msg:{}, error:{}", message, ex.getMessage(), ex);
                            })
                            .onErrorResume(ex -> {
                                // 错误恢复，继续处理其他消息
                                return Mono.empty();
                            });
                })
                .subscribeOn(Schedulers.boundedElastic())
                .then();
    }


//    @KafkaListener(topics = {"${kafka.topic.otc.edw.msg.resp}"}
//            , groupId = "${kafka.topic.consumer.group.id.otc.edw.msg.resp}")
    public void messageSendListener(List<ConsumerRecord<String, String>> records) {
        for (ConsumerRecord<String, String> record : records) {
            String message = record.value();
            log.info("messageSendListener receive msg: {},offset:{}", message, record.offset());
            try {
                SendMessageRequest request = JSON.parseObject(message, SendMessageRequest.class);
                messageService.sendMessage(request)
                        .subscribe(
                                success -> {},
                                error -> {

                                }
                        );
            } catch (Exception e) {

            }
        }
    }
}
