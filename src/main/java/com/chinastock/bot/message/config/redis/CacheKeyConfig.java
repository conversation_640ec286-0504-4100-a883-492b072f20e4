package com.chinastock.bot.message.config.redis;

import com.alibaba.fastjson2.JSON;
import org.springframework.cache.interceptor.KeyGenerator;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Arrays;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/2/15 11:40
 **/
@Configuration
public class CacheKeyConfig {

    @Bean("cacheKeyGenerator")
    public KeyGenerator keyGenerator() {
        return (target, method, params) -> (target.getClass().getSimpleName()
                + ":" + method.getName() + ":" + JSON.toJSONString(Arrays.asList(params)));
    }
}
