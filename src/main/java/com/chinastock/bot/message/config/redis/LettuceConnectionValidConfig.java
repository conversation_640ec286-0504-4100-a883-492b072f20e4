package com.chinastock.bot.message.config.redis;

import jakarta.annotation.Resource;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/4/19 9:11
 */
@Component
public class LettuceConnectionValidConfig implements InitializingBean {

    @Resource
    private RedisConnectionFactory redisConnectionFactory;

    @Override
    public void afterPropertiesSet(){
        if(redisConnectionFactory instanceof LettuceConnectionFactory){
            LettuceConnectionFactory c=(LettuceConnectionFactory)redisConnectionFactory;
            c.setValidateConnection(true);
        }
    }
}
