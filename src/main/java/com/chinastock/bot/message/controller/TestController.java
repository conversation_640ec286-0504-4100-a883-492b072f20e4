package com.chinastock.bot.message.controller;

import cn.com.chinastock.cnf.core.http.BaseResponse;
import cn.com.chinastock.cnf.core.http.Meta;
import com.chinastock.bot.message.service.MessageService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.env.Environment;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

/**
 * @Author: David
 * @Date: 2025/8/26 15:17
 * @Description:
 */
@Tag(name = "测试接口")
@RestController
@Slf4j
public class TestController {

    @Resource
    private Environment environment;

    @Resource
    private MessageService messageService;

    @Operation(summary = "发送测试消息", description = "发送测试消息")
    @RequestMapping(value = "/testSendMessage", method = RequestMethod.POST)
    public Mono<BaseResponse<Boolean>> testSendMessage(@RequestBody String msg) {
        return Mono.fromCallable(() -> messageService.testSendMessage(msg))
                .subscribeOn(Schedulers.boundedElastic())
                .map(t -> new BaseResponse<>(new Meta(), t));
    }


    @Operation(summary = "获取环境变量", description = "获取环境变量")
    @RequestMapping(value = "/env/{request}", method = RequestMethod.POST)
    public Mono<BaseResponse<String>> sendMessage(@PathVariable String request) {
        String property = environment.getProperty(request);
        return Mono.just(new BaseResponse<>(new Meta(), property));
    }
}
