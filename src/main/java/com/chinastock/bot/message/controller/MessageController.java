package com.chinastock.bot.message.controller;

import cn.com.chinastock.cnf.core.http.BaseResponse;
import cn.com.chinastock.cnf.core.http.Meta;
import com.chinastock.bot.message.pojo.request.SendMessageRequest;
import com.chinastock.bot.message.pojo.vo.send.MessageSendResponse;
import com.chinastock.bot.message.service.MessageService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

/**
 * @Author: David
 * @Date: 2025/8/26 15:17
 * @Description:
 */
@Tag(name = "消息接口")
@RestController
@Slf4j
public class MessageController {

    @Resource
    private MessageService messageService;


    @Operation(summary = "发送消息", description = "发送消息")
    @RequestMapping(value = "/message/send", method = RequestMethod.POST)
    public Mono<BaseResponse<MessageSendResponse>> sendMessage(@RequestBody SendMessageRequest request) {
        Mono<MessageSendResponse> messageSendResponseMono = messageService.sendMessage(request);
        return messageSendResponseMono.map(t -> new BaseResponse<>(new Meta(), t));
    }
}
