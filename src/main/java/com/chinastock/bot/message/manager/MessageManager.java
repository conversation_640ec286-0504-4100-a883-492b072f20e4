package com.chinastock.bot.message.manager;

import com.chinastock.bot.message.pojo.dto.CustMessage;
import com.chinastock.bot.message.pojo.vo.message.ReceiveMessageVo;

/**
 * @Author: David
 * @Date: 2025/8/25 17:10
 * @Description:
 */
public interface MessageManager {

    CustMessage saveMessage(ReceiveMessageVo message, String depNo);


    boolean checkRepeat(String pre, String msgId);

    void delRepeat(String pre, String msgId);
}
