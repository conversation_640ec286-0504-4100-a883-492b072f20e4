package com.chinastock.bot.message.manager.remote;

import cn.com.chinastock.cnf.webclient.WebClientExchange;
import com.chinastock.bot.message.pojo.request.SendMessageRequest;
import com.chinastock.bot.message.pojo.vo.send.MessageSendResponse;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.service.annotation.PostExchange;
import reactor.core.publisher.Mono;

/**
 * @Author: David
 * @Date: 2025/8/27 09:22
 * @Description:
 */
@WebClientExchange(name = "message-send")
public interface MessageSendManager {

    @PostExchange("/message/send")
    Mono<MessageSendResponse> sendMessage(@RequestBody SendMessageRequest message);

}
