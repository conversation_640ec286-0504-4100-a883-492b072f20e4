package com.chinastock.bot.message.manager.impl;

import cn.com.chinastock.cnf.core.log.GalaxyLoggerFactory;
import cn.com.chinastock.cnf.core.log.IGalaxyLogger;
import com.alibaba.fastjson2.JSON;
import com.chinastock.bot.message.manager.MqManager;
import jakarta.annotation.Resource;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.support.SendResult;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import java.util.concurrent.CompletableFuture;

/**
 * @Author: David
 * @Date: 2025/8/26 14:03
 * @Description:
 */
@Service
public class MqManagerImpl implements MqManager {

    private static final IGalaxyLogger log = GalaxyLoggerFactory.getLogger(MqManagerImpl.class);

    @Resource
    private KafkaTemplate<String, String> kafkaTemplateForString;



    /**
     * 发送单个消息（使用String key）
     *
     * @return 发送结果的CompletableFuture
     */
    @Override
    public CompletableFuture<SendResult<String, String>> sendMessage(String topic, Object obj) {
        String message;
        if(obj instanceof String) {
            message = (String) obj;
        }else{
            message = JSON.toJSONString(obj);
        }
        log.info("before send message: {}", message);
        CompletableFuture<SendResult<String, String>> kafkaFuture =
                kafkaTemplateForString.send(topic, message);

        return kafkaFuture.whenComplete((result, ex) -> {
            if (ex != null) {
                log.error("send message failed, topic: {}, msg:{}", topic, message, ex);
            } else {
                log.info("send message success, topic: {}, msg:{}, partition: {}, offset: {}",
                        result.getRecordMetadata().topic(), message,
                        result.getRecordMetadata().partition(), result.getRecordMetadata().offset());
            }
        });
    }

    /**
     * 发送单个消息（使用String key）- Reactive版本
     *
     * @return 发送结果的Mono
     */
    @Override
    public Mono<SendResult<String, String>> sendMessageReactive(String topic, Object obj) {
        String message;
        if(obj instanceof String) {
            message = (String) obj;
        }else{
            message = JSON.toJSONString(obj);
        }
        log.info("before send message: {}", message);

        return Mono.fromFuture(kafkaTemplateForString.send(topic, message))
                .doOnSuccess(result -> {
                    log.info("send message success, topic: {}, msg:{}, partition: {}, offset: {}",
                            result.getRecordMetadata().topic(), message,
                            result.getRecordMetadata().partition(), result.getRecordMetadata().offset());
                })
                .doOnError(ex -> {
                    log.error("send message failed, topic: {}, msg:{}", topic, message, ex);
                });
    }




}
