package com.chinastock.bot.message.manager;

import org.springframework.kafka.support.SendResult;

import java.util.concurrent.CompletableFuture;

/**
 * @Author: <PERSON>
 * @Date: 2025/8/26 14:03
 * @Description:
 */
public interface MqManager {

    /**
     * 发送消息
     *
     * @param topic
     * @param obj
     * @return
     */
    CompletableFuture<SendResult<String, String>> sendMessage(String topic, Object obj);




}
