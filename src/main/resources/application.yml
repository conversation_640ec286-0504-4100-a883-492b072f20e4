# Galaxy Boot相关配置
galaxy:
  system:
    code: ODM  # 系统三字码，需配置到该配置文件下，不支持apollo配置
  log:
    request-response:
      enabled: true            # 是否打印RequestLog和ResponseLog，默认为true
      request-headers: true    # 是否打印Request Header，默认不打印(仅在request-response.enabled=true时生效)
      response-headers: true  # 是否打印Response Header，默认不打印(仅在request-response.enabled=true时生效)
      mask-field: false         # 是否开启RequestLog和ResponseLog中字段的掩码处理，默认为false。设置为true后，被注解@MaskedField标记的字段会被掩码处理为"***"
    performance:
      enabled: true            # 是否打印性能日志，默认为true
    default-category: APP_LOG
    exception-pretty-print: true # 开启后，会将异常日志多打一条换行后的日志，生产关闭
  kafka:
    enabled: true

server:
  port: 8085

spring:
  application:
    name: bot-message
  profiles:
    active: dev

# Kafka配置 - 用于测试
kafka:
  topic:
    edw:
      msg:
        tmp: test-edw-msg-tmp-topic
        req: test-edw-msg-req-topic
